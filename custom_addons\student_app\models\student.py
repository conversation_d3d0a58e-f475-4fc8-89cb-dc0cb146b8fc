# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
import re
from typing import List, Tuple


class Student(models.Model):
    _name = 'student.student'
    _description = 'Student'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    # Basic Information
    name = fields.Char(
        string='Full Name',
        required=True,
        tracking=True,
        help="Student's full name"
    )
    
    student_id = fields.Char(
        string='Student ID',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: self._generate_student_id(),
        help="Unique student identification number"
    )
    
    email = fields.Char(
        string='Email',
        required=True,
        tracking=True,
        help="Student's email address"
    )
    
    phone = fields.Char(
        string='Phone',
        tracking=True,
        help="Student's phone number"
    )
    
    date_of_birth = fields.Date(
        string='Date of Birth',
        tracking=True,
        help="Student's date of birth"
    )
    
    age = fields.Integer(
        string='Age',
        compute='_compute_age',
        store=True,
        help="Student's age calculated from date of birth"
    )
    
    gender = fields.Selection([  # type: ignore
        ('male', 'Male'),
        ('female', 'Female'),
        ('other', 'Other')
    ], string='Gender', tracking=True)
    
    # Address Information
    street = fields.Char(string='Street')
    street2 = fields.Char(string='Street 2')
    city = fields.Char(string='City')
    state_id = fields.Many2one('res.country.state', string='State')
    country_id = fields.Many2one('res.country', string='Country')
    zip = fields.Char(string='ZIP Code')
    
    # Academic Information
    enrollment_date = fields.Date(
        string='Enrollment Date',
        default=fields.Date.today,
        required=True,
        tracking=True,
        help="Date when student enrolled"
    )
    
    status = fields.Selection([  # type: ignore
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('graduated', 'Graduated'),
        ('suspended', 'Suspended')
    ], string='Status', default='active', required=True, tracking=True)
    
    grade_level = fields.Selection([  # type: ignore
        ('freshman', 'Freshman'),
        ('sophomore', 'Sophomore'),
        ('junior', 'Junior'),
        ('senior', 'Senior'),
        ('graduate', 'Graduate')
    ], string='Grade Level', required=True, tracking=True)
    
    gpa = fields.Float(
        string='GPA',
        digits=(3, 2),
        help="Grade Point Average"
    )
    
    # Relationships
    enrollment_ids = fields.One2many(
        'student.enrollment',
        'student_id',
        string='Course Enrollments'
    )
    
    course_count = fields.Integer(
        string='Number of Courses',
        compute='_compute_course_count'
    )
    
    # Image
    image = fields.Image(
        string='Photo',
        max_width=1024,
        max_height=1024
    )
    
    # Additional Information
    notes = fields.Text(string='Notes')
    active = fields.Boolean(string='Active', default=True)
    
    # Computed Fields
    @api.depends('date_of_birth')
    def _compute_age(self):
        """Compute student age from date of birth"""
        today = fields.Date.today()
        for student in self:
            if student.date_of_birth:
                student.age = today.year - student.date_of_birth.year - (
                    (today.month, today.day) < (student.date_of_birth.month, student.date_of_birth.day)
                )
            else:
                student.age = 0
    
    @api.depends('enrollment_ids')
    def _compute_course_count(self):
        """Compute number of enrolled courses"""
        for student in self:
            student.course_count = len(student.enrollment_ids)
    
    # Constraints and Validations
    @api.constrains('email')
    def _check_email(self):
        """Validate email format"""
        for student in self:
            if student.email and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', student.email):
                raise ValidationError("Please enter a valid email address.")
    
    @api.constrains('gpa')
    def _check_gpa(self):
        """Validate GPA range"""
        for student in self:
            if student.gpa and (student.gpa < 0 or student.gpa > 4.0):
                raise ValidationError("GPA must be between 0.0 and 4.0")
    
    @api.constrains('date_of_birth')
    def _check_date_of_birth(self):
        """Validate date of birth"""
        for student in self:
            if student.date_of_birth and student.date_of_birth > fields.Date.today():
                raise ValidationError("Date of birth cannot be in the future.")
    
    # Methods
    def _generate_student_id(self):
        """Generate unique student ID"""
        sequence = self.env['ir.sequence'].next_by_code('student.student') or '0001'
        return f"STU{sequence}"
    
    @api.model
    def create(self, vals):
        """Override create to generate student ID"""
        if not vals.get('student_id'):
            vals['student_id'] = self._generate_student_id()
        return super(Student, self).create(vals)
    
    def action_view_enrollments(self):
        """Action to view student enrollments"""
        self.ensure_one()
        return {
            'name': f'Enrollments - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'student.enrollment',
            'view_mode': 'tree,form',
            'domain': [('student_id', '=', self.id)],
            'context': {'default_student_id': self.id},
        }
    
    def action_graduate(self):
        """Action to graduate student"""
        for student in self:
            student.status = 'graduated'
            student.message_post(body=f"Student {student.name} has graduated!")
    
    def action_suspend(self):
        """Action to suspend student"""
        for student in self:
            student.status = 'suspended'
            student.message_post(body=f"Student {student.name} has been suspended.")
    
    def action_reactivate(self):
        """Action to reactivate student"""
        for student in self:
            student.status = 'active'
            student.message_post(body=f"Student {student.name} has been reactivated.")
